import * as THREE from 'three';
import AIService from './aiService';

// Mock THREE.Box3
jest.mock('three', () => {
  const originalThree = jest.requireActual('three');
  return {
    ...originalThree,
    Box3: jest.fn().mockImplementation(() => ({
      setFromObject: jest.fn().mockReturnThis(),
      getSize: jest.fn().mockReturnValue(new originalThree.Vector3(1, 1, 1)),
      getCenter: jest.fn().mockReturnValue(new originalThree.Vector3(0, 0, 0)),
      isEmpty: jest.fn().mockReturnValue(false),
    })),
  };
});

// Mock fetch for API calls
global.fetch = jest.fn();

describe('AIService', () => {
  let aiService: AIService;
  let mockScene: THREE.Scene;
  let mockMesh: THREE.Mesh;

  beforeEach(() => {
    aiService = new AIService();
    mockScene = new THREE.Scene();
    mockMesh = new THREE.Mesh(
      new THREE.BoxGeometry(1, 1, 1),
      new THREE.MeshBasicMaterial({ color: 0xff0000 })
    );
    mockMesh.uuid = 'test-uuid';
    mockScene.add(mockMesh);

    // Reset mocks
    (global.fetch as jest.Mock).mockReset();
  });

  describe('analyzeScene', () => {
    it('should use basic analysis when no API key is available', async () => {
      // Ensure no API key is set
      aiService.setApiKey('');
      
      // Spy on the analyzeSceneBasic method
      const analyzeSceneBasicSpy = jest.spyOn(aiService as any, 'analyzeSceneBasic');
      
      await aiService.analyzeScene(mockScene);
      
      // Verify analyzeSceneBasic was called
      expect(analyzeSceneBasicSpy).toHaveBeenCalled();
      
      // Verify object was registered with a name
      const registeredObjects = aiService.getRegisteredObjects();
      expect(registeredObjects.length).toBeGreaterThan(0);
      expect(registeredObjects[0].uuid).toBe('test-uuid');
    });

    it('should attempt to use AI analysis when API key is available', async () => {
      // Set a mock API key
      aiService.setApiKey('test-api-key');
      
      // Mock successful API response
      const mockResponse = {
        content: [{ type: 'text', text: '```json\n{"objects":[{"uuid":"test-uuid","name":"cube","category":"furniture","confidence":90}]}\n```' }],
      };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockResponse),
      });
      
      // Spy on the analyzeSceneWithAI method
      const analyzeSceneWithAISpy = jest.spyOn(aiService as any, 'analyzeSceneWithAI');
      
      await aiService.analyzeScene(mockScene);
      
      // Verify analyzeSceneWithAI was called
      expect(analyzeSceneWithAISpy).toHaveBeenCalled();
      
      // Verify API call was made
      expect(global.fetch).toHaveBeenCalled();
      
      // Verify object was registered with AI-provided name
      const registeredObjects = aiService.getRegisteredObjects();
      expect(registeredObjects.length).toBe(1);
      expect(registeredObjects[0].name).toBe('cube');
      expect(registeredObjects[0].uuid).toBe('test-uuid');
    });

    it('should fall back to basic analysis if AI analysis fails', async () => {
      // Set a mock API key
      aiService.setApiKey('test-api-key');
      
      // Mock failed API response
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API error'));
      
      // Spy on the analyzeSceneBasic method
      const analyzeSceneBasicSpy = jest.spyOn(aiService as any, 'analyzeSceneBasic');
      
      await aiService.analyzeScene(mockScene);
      
      // Verify analyzeSceneBasic was called as fallback
      expect(analyzeSceneBasicSpy).toHaveBeenCalled();
      
      // Verify object was registered with a name
      const registeredObjects = aiService.getRegisteredObjects();
      expect(registeredObjects.length).toBeGreaterThan(0);
    });
  });
});
