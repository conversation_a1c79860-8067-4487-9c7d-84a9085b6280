import * as THREE from 'three';

// Define types for AI response
export interface AISceneChange {
  objectId: string;
  changes: {
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    scale?: { x: number; y: number; z: number };
    visible?: boolean;
  };
}

export interface AIResponse {
  changes: AISceneChange[];
  explanation: string;
}

// Claude API types
interface ClaudeMessage {
  role: 'user' | 'assistant';
  content: string | ClaudeContent[];
}

interface ClaudeContent {
  type: 'text';
  text: string;
}

interface ClaudeAPIRequest {
  model: string;
  messages: ClaudeMessage[];
  max_tokens: number;
  temperature?: number;
  system?: string;
}

interface ClaudeAPIResponse {
  id: string;
  type: string;
  role: string;
  content: ClaudeContent[];
  model: string;
  stop_reason: string;
  stop_sequence: string | null;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
}

// Available AI service types
export type AIServiceType = 'claude' | 'gemini';

// AI service configuration
interface AIServiceConfig {
  apiEndpoint: string;
  model: string;
}

// AI service configurations
const AI_SERVICE_CONFIGS: Record<AIServiceType, AIServiceConfig> = {
  claude: {
    apiEndpoint: '/api/claude',
    model: 'claude-3-opus-20240229'
  },
  gemini: {
    apiEndpoint: '/api/gemini',
    model: 'gemini-pro'
  }
};

// Real implementation of AI service
export class AIService {
  // Map common object names to their UUIDs in the scene
  private objectNameMap: Map<string, string> = new Map();
  private apiEndpoint: string;
  private model: string;
  private serviceType: AIServiceType;

  constructor(serviceType: AIServiceType = 'claude') {
    // Set the service type and configuration
    this.serviceType = serviceType;
    const config = AI_SERVICE_CONFIGS[serviceType];
    this.apiEndpoint = config.apiEndpoint;
    this.model = config.model;

    console.log(`AIService initialized with ${serviceType} service`);
  }

  // Register objects with human-readable names
  registerObject(name: string, uuid: string): void {
    this.objectNameMap.set(name.toLowerCase(), uuid);
  }

  // Clear registered objects
  clearObjects(): void {
    this.objectNameMap.clear();
  }

  // Get all registered objects
  getRegisteredObjects(): { name: string; uuid: string }[] {
    return Array.from(this.objectNameMap.entries()).map(([name, uuid]) => ({
      name,
      uuid,
    }));
  }

  // Analyze the scene to identify objects
  async analyzeScene(
    scene: THREE.Scene,
    useFallback: boolean = false,
  ): Promise<void> {
    // Clear existing mappings
    this.objectNameMap.clear();

    // Extract objects from the scene
    const objects: THREE.Mesh[] = [];
    scene.traverse(object => {
      if (object instanceof THREE.Mesh) {
        objects.push(object);
      }
    });

    // If no objects found, return early
    if (objects.length === 0) {
      console.log('No objects found in scene to analyze');
      throw new Error('No objects found in scene to analyze');
    }

    // If useFallback is true, use basic analysis directly
    if (useFallback) {
      console.log('Using basic analysis as requested');
      this.analyzeSceneBasic(objects);
      return;
    }

    // Try to use AI for object identification
    try {
      // Always try to use AI first
      await this.analyzeSceneWithAI(scene, objects);
    } catch (error) {
      console.error('Error analyzing scene with AI:', error);

      // If this is a 400-level error, propagate it up
      if (error instanceof Error) {
        const errorMessage = error.message;
        if (
          errorMessage.includes('400') ||
          errorMessage.includes('401') ||
          errorMessage.includes('403') ||
          errorMessage.includes('429')
        ) {
          // This is a client error, propagate it up
          throw error;
        }
      }

      // For other errors, fallback to basic analysis
      console.log('Falling back to basic analysis due to error');
      this.analyzeSceneBasic(objects);
    }
  }

  // Use AI to analyze and identify objects in the scene
  private async analyzeSceneWithAI(
    _scene: THREE.Scene,
    objects: THREE.Mesh[],
  ): Promise<void> {
    console.log(`Starting AI analysis with ${this.serviceType} service...`);
    // Validate objects
    if (objects.length === 0) {
      throw new Error('No objects found in scene to analyze');
    }

    // Log object count
    console.log(`Analyzing ${objects.length} objects in scene`);

    // Extract features for each object
    const objectFeatures = objects.map(object => {
      // Ensure object has a valid UUID
      if (!object.uuid) {
        console.warn('Object missing UUID, generating one');
        object.uuid = THREE.MathUtils.generateUUID();
      }

      const box = new THREE.Box3().setFromObject(object);
      const size = box.getSize(new THREE.Vector3());
      const center = box.getCenter(new THREE.Vector3());

      // Get material information
      let materialInfo = 'unknown material';
      if (object.material) {
        if (Array.isArray(object.material)) {
          materialInfo = `multiple materials (${object.material.length})`;
        } else if (object.material instanceof THREE.Material) {
          materialInfo = object.material.type;
          // Try to get color information if available
          if ('color' in object.material && object.material.color) {
            const material = object.material as THREE.MeshBasicMaterial;
            if (
              material.color &&
              typeof material.color.getHexString === 'function'
            ) {
              materialInfo += ` (color: ${material.color.getHexString()})`;
            }
          }
        }
      }

      // Get geometry information
      let geometryInfo = 'unknown geometry';
      if (object.geometry) {
        geometryInfo = object.geometry.type;
        if (object.geometry instanceof THREE.BufferGeometry) {
          const vertexCount = object.geometry.attributes.position
            ? object.geometry.attributes.position.count
            : 'unknown';
          geometryInfo += ` (vertices: ${vertexCount})`;
        }
      }

      return {
        uuid: object.uuid,
        name: object.name || 'unnamed',
        dimensions: {
          width: size.x,
          height: size.y,
          depth: size.z,
        },
        position: {
          x: center.x,
          y: center.y,
          z: center.z,
        },
        material: materialInfo,
        geometry: geometryInfo,
      };
    });

    // Construct prompt for Claude
    const systemPrompt = `You are an AI assistant that helps identify 3D objects in a scene.
Your task is to analyze the provided 3D object features and identify what each object likely represents.

For each object, provide:
1. A descriptive name (like "dining_table", "office_chair", "floor_lamp", etc.)
2. A category (furniture, decoration, structural, etc.)
3. A confidence score (0-100)

Respond in a specific JSON format that the application can parse:
{
  "objects": [
    {
      "uuid": "the-uuid-from-input",
      "name": "descriptive_name",
      "category": "object-category",
      "confidence": 85
    }
  ]
}

Notes:
- Use underscores in names (e.g., "coffee_table" not "coffee table")
- Be specific but concise in naming
- Base your analysis on dimensions, position, and other provided features
- If you're very uncertain, use generic names like "small_object", "large_furniture", etc.
- Always include all objects from the input in your response`;

    const objectsDescription = JSON.stringify(objectFeatures, null, 2);
    const userMessage = `Here are the 3D objects in the scene. Please identify them:\n\n${objectsDescription}\n\nPlease provide the identifications in the required JSON format.`;

    // Call AI API
    console.log(`Calling ${this.serviceType} API for scene analysis...`);
    const response = await this.callAIAPI(systemPrompt, userMessage);
    console.log(`Received response from ${this.serviceType} API`);

    // Parse the response
    try {
      // Extract text content from the response
      let content = '';

      if (response.content && Array.isArray(response.content)) {
        content = response.content
          .filter(item => item.type === 'text')
          .map(item => item.text)
          .join('');
      }

      // Extract JSON from the response
      const jsonMatch =
        content.match(/```json\n([\s\S]*?)\n```/) ||
        content.match(/```\n([\s\S]*?)\n```/) ||
        content.match(/{[\s\S]*?}/);

      if (!jsonMatch) {
        throw new Error(
          `Could not extract JSON from ${this.serviceType} response`,
        );
      }

      // Parse the JSON
      const jsonStr = jsonMatch[0].startsWith('{')
        ? jsonMatch[0]
        : jsonMatch[1];
      const parsedResponse = JSON.parse(jsonStr);

      // Validate the response structure
      if (!parsedResponse.objects || !Array.isArray(parsedResponse.objects)) {
        throw new Error(`Invalid response structure from ${this.serviceType}`);
      }

      // Define interface for AI response object
      interface AIIdentifiedObject {
        uuid: string;
        name: string;
        category?: string;
        confidence?: number;
      }

      // Register the objects with their AI-identified names
      parsedResponse.objects.forEach((obj: AIIdentifiedObject) => {
        if (obj.uuid && obj.name) {
          this.registerObject(obj.name, obj.uuid);
        }
      });

      console.log(
        `${this.serviceType} successfully identified objects:`,
        parsedResponse.objects,
      );
    } catch (error) {
      console.error('Error parsing AI object identification response:', error);
      throw error;
    }
  }

  // Fallback method using basic heuristics
  private analyzeSceneBasic(objects: THREE.Mesh[]): void {
    // Use object properties to guess names
    let tableCount = 0;
    let chairCount = 0;
    let sofaCount = 0;
    let lampCount = 0;
    let genericCount = 0;

    objects.forEach(object => {
      // Simple heuristic based on object dimensions to guess what it might be
      const box = new THREE.Box3().setFromObject(object);
      const size = box.getSize(new THREE.Vector3());
      const height = size.y;
      const width = Math.max(size.x, size.z);

      let name = '';

      if (height < 1 && width > 1) {
        name = `table_${++tableCount}`;
      } else if (height < 1.5 && height > 0.5 && width < 1) {
        name = `chair_${++chairCount}`;
      } else if (height < 1 && width > 2) {
        name = `sofa_${++sofaCount}`;
      } else if (height > 1.5 && width < 0.5) {
        name = `lamp_${++lampCount}`;
      } else {
        name = `object_${++genericCount}`;
      }

      this.registerObject(name, object.uuid);
    });
  }

  // Process an AI prompt and return scene changes
  async processPrompt(prompt: string, scene: THREE.Scene): Promise<AIResponse> {
    // Analyze scene if no objects are registered
    if (this.objectNameMap.size === 0) {
      await this.analyzeScene(scene);
    }

    try {
      // Prepare the scene description for Claude
      const sceneDescription = this.generateSceneDescription(scene);

      // Prepare the registered objects list
      const objectsList = this.getRegisteredObjects()
        .map(obj => `- ${obj.name} (ID: ${obj.uuid})`)
        .join('\n');

      // Construct the prompt for Claude
      const systemPrompt = `You are an AI assistant that helps users manipulate 3D objects in a scene.
Your task is to interpret the user's request and generate specific instructions for how to modify objects in the scene.

The scene contains the following objects:
${objectsList}

You must respond in a specific JSON format that the application can parse:
{
  "changes": [
    {
      "objectId": "uuid-of-object-to-change",
      "changes": {
        "position": { "x": number, "y": number, "z": number },
        "rotation": { "x": number, "y": number, "z": number },
        "scale": { "x": number, "y": number, "z": number },
        "visible": boolean
      }
    }
  ],
  "explanation": "A human-readable explanation of the changes you made"
}

Notes:
- Only include properties that should be changed
- Rotation values should be in radians
- Position (0,0,0) is the center of the scene
- The Y axis points up
- For rotation, use standard Three.js Euler angles (XYZ order)
- Be precise and specific in your changes
- Provide a clear explanation of what you did`;

      const userMessage = `Scene description: ${sceneDescription}\n\nUser request: ${prompt}\n\nPlease provide the changes in the required JSON format.`;

      // Make API call
      console.log(`Calling ${this.serviceType} API for prompt processing...`);
      const response = await this.callAIAPI(systemPrompt, userMessage);
      console.log(`Received response from ${this.serviceType} API for prompt`);

      // Parse the response to extract the JSON
      return this.parseAIResponse(response);
    } catch (error) {
      console.error(`Error calling ${this.serviceType} API:`, error);

      // Fallback to mock implementation if API call fails
      return this.processMockPrompt(prompt);
    }
  }

  // Call AI API via our Next.js API route
  private async callAIAPI(
    systemPrompt: string,
    userMessage: string,
  ): Promise<ClaudeAPIResponse> {
    const requestBody: ClaudeAPIRequest = {
      model: this.model,
      messages: [
        {
          role: 'user',
          content: userMessage,
        },
      ],
      max_tokens: 4000,
      temperature: 0.7,
      system: systemPrompt,
    };

    // We don't need to check for API key here since it's handled by the API route
    // The API key is stored securely on the server side

    try {
      console.log(`Calling ${this.serviceType} API via ${this.apiEndpoint}...`);

      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        console.log(
          `Response not OK: ${response.status} ${response.statusText}`,
        );
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(
            errorData.error ||
              `API error (${response.status}): ${response.statusText}`,
          );
        } catch {
          // If we can't parse the response as JSON, try to get the text
          const errorText = await response.text();
          console.log('Error text:', errorText);
          throw new Error(
            `API error (${response.status}): ${errorText || response.statusText}`,
          );
        }
      }

      const responseData = await response.json();
      console.log(`Received response from ${this.serviceType} API`);

      return responseData;
    } catch (error) {
      console.error(
        `Error calling ${this.serviceType} API via Next.js route:`,
        error,
      );
      throw error;
    }
  }

  // Parse AI API response to extract the JSON
  private parseAIResponse(response: ClaudeAPIResponse): AIResponse {
    try {
      // Extract text content from the response
      // We need to handle different response formats
      let content = '';

      if (response.content && Array.isArray(response.content)) {
        content = response.content
          .filter(item => item.type === 'text')
          .map(item => item.text)
          .join('');
      }

      // Extract JSON from the response
      const jsonMatch =
        content.match(/```json\n([\s\S]*?)\n```/) ||
        content.match(/```\n([\s\S]*?)\n```/) ||
        content.match(/{[\s\S]*?}/);

      if (!jsonMatch) {
        throw new Error(
          `Could not extract JSON from ${this.serviceType} response`,
        );
      }

      // Parse the JSON
      const jsonStr = jsonMatch[0].startsWith('{')
        ? jsonMatch[0]
        : jsonMatch[1];
      const parsedResponse = JSON.parse(jsonStr) as AIResponse;

      // Validate the response structure
      if (
        !parsedResponse.changes ||
        !Array.isArray(parsedResponse.changes) ||
        !parsedResponse.explanation
      ) {
        throw new Error(`Invalid response structure from ${this.serviceType}`);
      }

      return parsedResponse;
    } catch (error) {
      console.error(`Error parsing ${this.serviceType} response:`, error);
      throw new Error(`Failed to parse ${this.serviceType} response`);
    }
  }

  // Generate a description of the scene for Claude
  private generateSceneDescription(scene: THREE.Scene): string {
    let description = 'The scene contains:';

    // Add registered objects to the description
    this.getRegisteredObjects().forEach(obj => {
      // Find the object in the scene
      let targetObject: THREE.Object3D | undefined;

      scene.traverse(object => {
        if (object.uuid === obj.uuid) {
          targetObject = object;
        }
      });

      if (targetObject) {
        description += `\n- ${
          obj.name
        } at position (${targetObject.position.x.toFixed(
          2,
        )}, ${targetObject.position.y.toFixed(
          2,
        )}, ${targetObject.position.z.toFixed(2)})`;
        description += ` with rotation (${targetObject.rotation.x.toFixed(
          2,
        )}, ${targetObject.rotation.y.toFixed(
          2,
        )}, ${targetObject.rotation.z.toFixed(2)})`;
        description += ` and scale (${targetObject.scale.x.toFixed(
          2,
        )}, ${targetObject.scale.y.toFixed(2)}, ${targetObject.scale.z.toFixed(
          2,
        )})`;
      }
    });

    return description;
  }

  // Fallback mock implementation for when API is not available
  private async processMockPrompt(prompt: string): Promise<AIResponse> {
    const promptLower = prompt.toLowerCase();
    const response: AIResponse = {
      changes: [],
      explanation: "I've made the following changes based on your request:",
    };

    // Simple keyword matching to simulate AI understanding

    // Example: "place table in the left top corner of the living room"
    if (
      promptLower.includes('table') &&
      promptLower.includes('left') &&
      promptLower.includes('corner')
    ) {
      // Find a table object
      const tableId = this.findObjectByNamePattern('table');
      if (tableId) {
        response.changes.push({
          objectId: tableId,
          changes: {
            position: { x: -5, y: 0, z: -5 },
          },
        });
        response.explanation +=
          '\n- Moved the table to the left top corner of the room.';
      }
    }

    // Example: "rotate the chair to face the window"
    else if (promptLower.includes('chair') && promptLower.includes('rotate')) {
      const chairId = this.findObjectByNamePattern('chair');
      if (chairId) {
        response.changes.push({
          objectId: chairId,
          changes: {
            rotation: { x: 0, y: Math.PI / 2, z: 0 },
          },
        });
        response.explanation += '\n- Rotated the chair to face the window.';
      }
    }

    // Example: "make the lamp taller"
    else if (promptLower.includes('lamp') && promptLower.includes('taller')) {
      const lampId = this.findObjectByNamePattern('lamp');
      if (lampId) {
        response.changes.push({
          objectId: lampId,
          changes: {
            scale: { x: 1, y: 1.5, z: 1 },
          },
        });
        response.explanation +=
          '\n- Made the lamp taller by increasing its height.';
      }
    }

    // If no specific patterns match, provide a generic response
    if (response.changes.length === 0) {
      response.explanation =
        "I couldn't determine specific changes to make based on your request. Please try being more specific about which objects you want to move and where.";
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return response;
  }

  // Helper method to find an object by name pattern
  private findObjectByNamePattern(pattern: string): string | undefined {
    for (const [name, uuid] of this.objectNameMap.entries()) {
      if (name.includes(pattern)) {
        return uuid;
      }
    }
    return undefined;
  }

  // Apply AI-suggested changes to the scene
  applyChanges(scene: THREE.Scene, changes: AISceneChange[]): void {
    changes.forEach(change => {
      // Find the object in the scene
      let targetObject: THREE.Object3D | undefined;

      scene.traverse(object => {
        if (object.uuid === change.objectId) {
          targetObject = object;
        }
      });

      if (targetObject) {
        // Apply position changes
        if (change.changes.position) {
          targetObject.position.set(
            change.changes.position.x,
            change.changes.position.y,
            change.changes.position.z,
          );
        }

        // Apply rotation changes
        if (change.changes.rotation) {
          targetObject.rotation.set(
            change.changes.rotation.x,
            change.changes.rotation.y,
            change.changes.rotation.z,
          );
        }

        // Apply scale changes
        if (change.changes.scale) {
          targetObject.scale.set(
            change.changes.scale.x,
            change.changes.scale.y,
            change.changes.scale.z,
          );
        }

        // Apply visibility changes
        if (change.changes.visible !== undefined) {
          targetObject.visible = change.changes.visible;
        }
      }
    });
  }
}

export default AIService;
