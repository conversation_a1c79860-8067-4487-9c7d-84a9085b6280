'use client';

import { useRef, useState, useCallback, useEffect } from 'react';
import * as THREE from 'three';

// Import utilities
import HistoryManager from './utils/historyManager';
import AIService, { AIServiceType } from './utils/aiService';
import { TransformMode } from './utils/objectTransformations';
import { SceneSetup } from './utils/sceneSetup';

// Import components
import ThreeViewer from './ThreeViewer';
import HelpPanel from './components/HelpPanel';
import ControlPanel from './components/ControlPanel';
import LoadingOverlay from './components/LoadingOverlay';
import AIPromptPanel from './components/AIPromptPanel';

interface ThreeViewerContainerProps {
  modelData: { url: string; fileType: string } | null;
  key?: string; // Add a key prop to force component remount when model is cleared
}

export default function ThreeViewerContainer({
  modelData,
}: ThreeViewerContainerProps) {
  // Refs
  const sceneSetupRef = useRef<SceneSetup | null>(null);
  const modelRef = useRef<THREE.Object3D | null>(null);
  const historyManagerRef = useRef<HistoryManager>(new HistoryManager());
  const aiServiceRef = useRef<AIService>(new AIService('claude'));

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedObjects, setSelectedObjects] = useState<THREE.Object3D[]>([]);
  const [transformMode, setTransformMode] =
    useState<TransformMode>('translate');
  const [transformSpeed, setTransformSpeed] = useState(0.1);
  const [showHelp, setShowHelp] = useState(false);

  // AI and history state
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [promptHistory, setPromptHistory] = useState<
    {
      prompt: string;
      response: string;
      timestamp: number;
    }[]
  >([]);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  // Model analysis state
  const [modelAnalyzed, setModelAnalyzed] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisError, setAnalysisError] = useState(false);
  const [analysisNotification, setAnalysisNotification] = useState<
    string | null
  >(null);

  // AI service state
  const [aiServiceType, setAiServiceType] = useState<AIServiceType>('claude');

  // Handle AI prompt submission
  const handleSubmitPrompt = useCallback(
    async (prompt: string) => {
      if (!sceneSetupRef.current || isProcessingAI) return;

      setIsProcessingAI(true);

      try {
        // Save current state before making changes
        historyManagerRef.current.addState(
          sceneSetupRef.current.scene,
          `Before AI prompt: "${prompt}"`,
        );

        // Process the prompt with AI
        const response = await aiServiceRef.current.processPrompt(
          prompt,
          sceneSetupRef.current.scene,
        );

        // Apply the changes to the scene
        aiServiceRef.current.applyChanges(
          sceneSetupRef.current.scene,
          response.changes,
        );

        // Save the new state after changes
        historyManagerRef.current.addState(
          sceneSetupRef.current.scene,
          `After AI prompt: "${prompt}"`,
        );

        // Update undo/redo availability
        setCanUndo(historyManagerRef.current.canUndo());
        setCanRedo(historyManagerRef.current.canRedo());

        // Add to prompt history
        setPromptHistory(prev => [
          ...prev,
          {
            prompt,
            response: response.explanation,
            timestamp: Date.now(),
          },
        ]);
      } catch (error) {
        console.error('Error processing AI prompt:', error);

        // Add error to prompt history
        setPromptHistory(prev => [
          ...prev,
          {
            prompt,
            response: `Error: ${
              error instanceof Error
                ? error.message
                : 'Failed to process prompt'
            }`,
            timestamp: Date.now(),
          },
        ]);
      } finally {
        setIsProcessingAI(false);
      }
    },
    [isProcessingAI],
  );

  // Handle undo
  const handleUndo = useCallback(() => {
    if (!sceneSetupRef.current || !historyManagerRef.current.canUndo()) return;

    historyManagerRef.current.undo(sceneSetupRef.current.scene);

    // Update undo/redo availability
    setCanUndo(historyManagerRef.current.canUndo());
    setCanRedo(historyManagerRef.current.canRedo());
  }, []);

  // Handle redo
  const handleRedo = useCallback(() => {
    if (!sceneSetupRef.current || !historyManagerRef.current.canRedo()) return;

    historyManagerRef.current.redo(sceneSetupRef.current.scene);

    // Update undo/redo availability
    setCanUndo(historyManagerRef.current.canUndo());
    setCanRedo(historyManagerRef.current.canRedo());
  }, []);

  // Toggle AI panel
  const handleToggleAIPanel = useCallback(() => {
    setShowAIPanel(prev => !prev);
  }, []);

  // Handle model analysis
  const handleAnalyzeModel = useCallback(async () => {
    if (!sceneSetupRef.current || !modelRef.current || isAnalyzing) return;

    setIsAnalyzing(true);
    setAnalysisError(false); // Reset error state

    try {
      console.log(`Starting model analysis with ${aiServiceType} service...`);

      // Clear any existing object mappings
      aiServiceRef.current.clearObjects();

      // Perform the analysis with AI
      await aiServiceRef.current.analyzeScene(sceneSetupRef.current.scene);

      // Mark as analyzed
      setModelAnalyzed(true);
      setAnalysisError(false);

      // Show notification
      setAnalysisNotification(
        'Model analysis complete! AI assistant is now ready.',
      );

      // Clear notification after 5 seconds
      setTimeout(() => {
        setAnalysisNotification(null);
      }, 5000);

      console.log('Model analysis complete');
    } catch (error) {
      console.error('Error analyzing model:', error);

      // Set error state to true to show fallback button
      setAnalysisError(true);

      // Show error notification with more details
      let errorMessage = 'Error analyzing model. Please try again.';

      if (error instanceof Error) {
        errorMessage = `Error analyzing model: ${error.message}`;

        // If it's a 400 error, add more helpful information
        if (error.message.includes('400')) {
          errorMessage +=
            '. This may be due to an invalid request format or content limitations.';
        }
        // If it's a 401/403 error, suggest checking API key
        else if (
          error.message.includes('401') ||
          error.message.includes('403')
        ) {
          errorMessage +=
            '. Please check that your API key is valid and has the necessary permissions.';
        }
        // If it's a 429 error, suggest rate limiting
        else if (error.message.includes('429')) {
          errorMessage +=
            '. The API rate limit has been exceeded. Please try again later.';
        }
      }

      setAnalysisNotification(errorMessage);

      // Log more details for debugging
      console.log('Analysis error details:', {
        error,
        errorMessage,
        aiService: aiServiceType,
        modelRef: modelRef.current ? 'Model exists' : 'No model',
        sceneRef: sceneSetupRef.current ? 'Scene exists' : 'No scene',
      });

      // Clear notification after 12 seconds (longer for error messages)
      setTimeout(() => {
        setAnalysisNotification(null);
      }, 12000);
    } finally {
      setIsAnalyzing(false);
    }
  }, [isAnalyzing, aiServiceRef, sceneSetupRef, modelRef, aiServiceType]);

  // Handle basic model analysis (fallback)
  const handleAnalyzeModelBasic = useCallback(async () => {
    if (!sceneSetupRef.current || !modelRef.current || isAnalyzing) return;

    setIsAnalyzing(true);

    try {
      console.log('Starting basic model analysis...');

      // Clear any existing object mappings
      aiServiceRef.current.clearObjects();

      // Perform the analysis with useFallback=true
      await aiServiceRef.current.analyzeScene(
        sceneSetupRef.current.scene,
        true,
      );

      // Mark as analyzed
      setModelAnalyzed(true);
      setAnalysisError(false);

      // Show notification
      setAnalysisNotification(
        'Basic model analysis complete. AI assistant is now ready with limited functionality.',
      );

      // Clear notification after 5 seconds
      setTimeout(() => {
        setAnalysisNotification(null);
      }, 5000);

      console.log('Basic model analysis complete');
    } catch (error) {
      console.error('Error in basic model analysis:', error);

      // Show error notification
      setAnalysisNotification(
        'Error in basic analysis. Please check the console for details.',
      );

      // Clear notification after 5 seconds
      setTimeout(() => {
        setAnalysisNotification(null);
      }, 5000);
    } finally {
      setIsAnalyzing(false);
    }
  }, [isAnalyzing, aiServiceRef, sceneSetupRef, modelRef]);

  // Reset model analyzed state when model changes
  useEffect(() => {
    // When model data changes, reset the analyzed state
    setModelAnalyzed(false);
    setAnalysisError(false);
  }, [modelData]);

  // Update AI service when service type changes
  useEffect(() => {
    console.log(`Switching AI service to ${aiServiceType}`);
    aiServiceRef.current = new AIService(aiServiceType);
    // Reset model analysis state when AI service changes
    setModelAnalyzed(false);
    setAnalysisError(false);
  }, [aiServiceType]);

  return (
    <>
      <ThreeViewer
        modelData={modelData}
        setIsLoading={setIsLoading}
        setError={setError}
        setSelectedObjects={setSelectedObjects}
        selectedObjects={selectedObjects}
        transformMode={transformMode}
        setTransformMode={setTransformMode}
        transformSpeed={transformSpeed}
        setTransformSpeed={setTransformSpeed}
        sceneSetupRef={sceneSetupRef}
        modelRef={modelRef}
        historyManagerRef={historyManagerRef}
        aiServiceRef={aiServiceRef}
        setCanUndo={setCanUndo}
        setCanRedo={setCanRedo}
      />

      {/* Analysis notification */}
      {analysisNotification && (
        <div
          className={`fixed top-4 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 animate-fade-in ${
            analysisNotification.includes('Error')
              ? 'bg-red-100 border border-red-400 text-red-700'
              : 'bg-green-100 border border-green-400 text-green-700'
          }`}
        >
          {analysisNotification}
        </div>
      )}

      <LoadingOverlay isLoading={isLoading} error={error} />

      {showHelp && (
        <HelpPanel
          transformMode={transformMode}
          onClose={() => setShowHelp(false)}
        />
      )}

      {showAIPanel && (
        <AIPromptPanel
          onSubmitPrompt={handleSubmitPrompt}
          promptHistory={promptHistory}
          isProcessing={isProcessingAI}
          modelAnalyzed={modelAnalyzed}
        />
      )}

      <ControlPanel
        selectedObjects={selectedObjects}
        transformMode={transformMode}
        transformSpeed={transformSpeed}
        toggleShowHelp={() => setShowHelp(prev => !prev)}
        canUndo={canUndo}
        canRedo={canRedo}
        onUndo={handleUndo}
        onRedo={handleRedo}
        showAIPanel={showAIPanel}
        onToggleAIPanel={handleToggleAIPanel}
        onClearSelection={() => {
          setSelectedObjects([]);
        }}
        modelLoaded={!!modelRef.current}
        modelAnalyzed={modelAnalyzed}
        isAnalyzing={isAnalyzing}
        onAnalyzeModel={handleAnalyzeModel}
        onAnalyzeModelBasic={handleAnalyzeModelBasic}
        analysisError={analysisError}
        aiServiceType={aiServiceType}
        onChangeAIService={setAiServiceType}
      />
    </>
  );
}
