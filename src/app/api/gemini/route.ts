import { NextRequest, NextResponse } from 'next/server';

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // 24 hours
    }
  });
}

export async function POST(request: NextRequest) {
  try {
    // Get API key from environment variables
    const apiKey = process.env.GEMINI_API_KEY;

    if (!apiKey) {
      return NextResponse.json(
        { error: 'Gemini API key not configured on server' },
        {
          status: 500,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          },
        },
      );
    }

    // Get request body
    const body = await request.json();

    // Extract system prompt and user message
    const { system, messages, model } = body;

    // Prepare Gemini API request
    // Gemini uses a different format than <PERSON>
    const geminiMessages = [];

    // Add system prompt as a system message if provided
    if (system) {
      geminiMessages.push({
        role: 'system',
        parts: [{ text: system }],
      });
    }

    // Add user messages
    if (messages && messages.length > 0) {
      // We expect the first message to be from the user
      const userMessage = messages[0];
      geminiMessages.push({
        role: 'user',
        parts: [{ text: userMessage.content }],
      });
    }

    // Gemini API endpoint - using updated model name
    const geminiEndpoint = `https://generativelanguage.googleapis.com/v1beta/models/${model || 'gemini-1.5-flash'}:generateContent?key=${apiKey}`;

    // Forward the request to Gemini API
    const response = await fetch(geminiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: geminiMessages,
        generationConfig: {
          temperature: body.temperature || 0.7,
          maxOutputTokens: body.max_tokens || 4000,
        },
      }),
    });

    // If the response is not ok, return the error
    if (!response.ok) {
      console.error(
        `Gemini API error: ${response.status} ${response.statusText}`,
      );

      let errorMessage = '';
      let errorDetails = {};
      try {
        // Try to parse as JSON first
        const errorJson = await response.json();
        console.error('Gemini API error details:', errorJson);
        errorMessage = JSON.stringify(errorJson);
        errorDetails = { ...(errorJson.error || {}) };
      } catch {
        // If not JSON, get as text
        const errorText = await response.text();
        console.error('Gemini API error text:', errorText);
        errorMessage = errorText || response.statusText;
      }

      return NextResponse.json(
        {
          error: `Gemini API error (${response.status}): ${errorMessage}`,
          details: errorDetails,
        },
        {
          status: response.status,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          },
        },
      );
    }

    // Get the Gemini response
    const geminiResponse = await response.json();

    // Transform Gemini response to match Claude response format
    const transformedResponse = {
      id: geminiResponse.candidates?.[0]?.content?.role || 'assistant',
      type: 'message',
      role: 'assistant',
      content:
        geminiResponse.candidates?.[0]?.content?.parts?.map(
          (part: { text: string }) => ({
            type: 'text',
            text: part.text,
          }),
        ) || [],
      model: model || 'gemini-1.5-flash',
      stop_reason: geminiResponse.candidates?.[0]?.finishReason || 'stop',
      stop_sequence: null,
      usage: {
        input_tokens: geminiResponse.usageMetadata?.promptTokenCount || 0,
        output_tokens: geminiResponse.usageMetadata?.candidatesTokenCount || 0,
      },
    };

    // Return the transformed response
    return NextResponse.json(transformedResponse, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Error in Gemini API route:', error);

    // Create error response with CORS headers
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
}
