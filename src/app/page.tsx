'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the ThreeViewerContainer component with SSR disabled
const ThreeViewerContainer = dynamic(
  () => import('../components/ThreeViewerContainer'),
  {
    ssr: false,
  },
);

export default function Home() {
  const [modelData, setModelData] = useState<{
    url: string;
    fileType: string;
  } | null>(null);
  const [fileInputKey, setFileInputKey] = useState(0);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      const fileType = file.name.split('.').pop()?.toLowerCase() || '';
      setModelData({ url, fileType });
    }
  };

  const handleClearModel = () => {
    if (modelData) {
      URL.revokeObjectURL(modelData.url);
      setModelData(null);
      setFileInputKey(prev => prev + 1);
    }
  };

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <header className="p-3 bg-gray-800 text-white">
        <h1 className="text-2xl font-bold">3D Model Viewer</h1>
      </header>

      <main className="flex-1 flex flex-col overflow-hidden">
        <div className="py-3 px-4 bg-gray-200 border-b border-gray-300 shadow-sm">
          <div className="flex flex-wrap gap-3 items-start">
            <div className="relative">
              <input
                key={fileInputKey}
                type="file"
                accept=".gltf,.glb,.obj,.fbx,.stl"
                onChange={handleFileChange}
                disabled={!!modelData}
                className={`border border-gray-400 p-1.5 rounded font-medium file:mr-3 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold ${
                  modelData
                    ? 'bg-gray-100 text-gray-500 cursor-not-allowed file:bg-gray-500 file:text-gray-200 file:cursor-not-allowed'
                    : 'bg-white text-gray-800 file:bg-blue-600 file:text-white hover:file:bg-blue-700'
                }`}
              />
            </div>
            {modelData && (
              <button
                onClick={handleClearModel}
                className="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded font-medium shadow-sm text-sm"
              >
                Clear Model
              </button>
            )}
            <p className="text-xs font-medium text-gray-700 ml-auto">
              Formats:{' '}
              <span className="font-bold">GLTF, GLB, OBJ, FBX, STL</span>
            </p>
          </div>
        </div>

        <div className="flex-1 relative h-[calc(100vh-7rem)]">
          <ThreeViewerContainer
            key={modelData ? modelData.url : `empty-${fileInputKey}`}
            modelData={modelData}
          />
        </div>
      </main>
    </div>
  );
}
